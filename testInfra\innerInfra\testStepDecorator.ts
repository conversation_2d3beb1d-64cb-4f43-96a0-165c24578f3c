import { test } from '@playwright/test';

/**
 * TestStep decorator that wraps method calls with <PERSON><PERSON>'s test.step
 * @param stepName - The name of the test step to display in the test report
 * @returns Method decorator
 */
export function TestStep(stepName: string) {
  return function decorator(target: Function) {
    return function replacementMethod(this, ...args) {
      return test.step(stepName, async () => await target.call(this, ...args));
    };
  };
}
